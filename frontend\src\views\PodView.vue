<template>
  <div class="min-h-screen bg-gradient-to-br from-[#EDF6FF] via-[#F5F5F5] to-[#FFF1DB]">
    <!-- Header -->
    <header class="bg-[#FFFFFF] shadow-sm border-b border-[#CCCCCC]/30">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
          <!-- Logo & Title -->
          <div class="flex items-center space-x-4">
            <div
              class="w-10 h-10 bg-gradient-to-br from-[#C1843E] to-[#704A1F] rounded-xl flex items-center justify-center">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
              </svg>
            </div>
            <div>
              <h1 class="text-xl font-bold text-[#111111]">{{ businessName }}</h1>
              <p class="text-sm text-[#4B4B4B]">Pod Dashboard</p>
            </div>
          </div>

          <!-- Navigation & User Menu -->
          <div class="flex items-center space-x-6">
            <nav class="hidden md:flex space-x-6">
              <button @click="activeTab = 'overview'"
                :class="activeTab === 'overview' ? 'text-[#1E4E79] border-b-2 border-[#1E4E79]' : 'text-[#4B4B4B] hover:text-[#1E4E79]'"
                class="pb-1 transition-colors duration-300">
                Overview
              </button>
              <button @click="activeTab = 'orders'"
                :class="activeTab === 'orders' ? 'text-[#1E4E79] border-b-2 border-[#1E4E79]' : 'text-[#4B4B4B] hover:text-[#1E4E79]'"
                class="pb-1 transition-colors duration-300">
                Orders
              </button>
              <button @click="activeTab = 'inventory'"
                :class="activeTab === 'inventory' ? 'text-[#1E4E79] border-b-2 border-[#1E4E79]' : 'text-[#4B4B4B] hover:text-[#1E4E79]'"
                class="pb-1 transition-colors duration-300">
                Inventory
              </button>
              <button @click="activeTab = 'reports'"
                :class="activeTab === 'reports' ? 'text-[#1E4E79] border-b-2 border-[#1E4E79]' : 'text-[#4B4B4B] hover:text-[#1E4E79]'"
                class="pb-1 transition-colors duration-300">
                Reports
              </button>
              <button @click="activeTab = 'income'"
                :class="activeTab === 'income' ? 'text-[#1E4E79] border-b-2 border-[#1E4E79]' : 'text-[#4B4B4B] hover:text-[#1E4E79]'"
                class="pb-1 transition-colors duration-300">
                Income
              </button>
            </nav>

            <!-- User Menu Dropdown -->
            <div class="relative" ref="userMenuRef">
              <button @click="showUserMenu = !showUserMenu"
                class="flex items-center space-x-2 p-2 rounded-xl hover:bg-[#F5F5F5] transition-colors duration-300">
                <div
                  class="w-8 h-8 bg-gradient-to-br from-[#1E4E79] to-[#132F4C] rounded-full flex items-center justify-center">
                  <span class="text-white text-sm font-semibold">P</span>
                </div>
                <svg class="w-4 h-4 text-[#4B4B4B] transition-transform duration-300"
                  :class="{ 'rotate-180': showUserMenu }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                </svg>
              </button>

              <!-- Dropdown Menu -->
              <div v-if="showUserMenu"
                class="absolute right-0 mt-2 w-48 bg-[#FFFFFF] rounded-xl shadow-lg border border-[#CCCCCC]/30 py-2 z-50">
                <div class="px-4 py-2 border-b border-[#CCCCCC]/30">
                  <p class="text-sm font-medium text-[#111111]">{{ businessName }}</p>
                  <p class="text-xs text-[#4B4B4B]">Pod Account</p>
                </div>
                <button @click="handleLogout" :disabled="isLoggingOut"
                  class="w-full text-left px-4 py-2 text-sm text-[#C62828] hover:bg-[#F5F5F5] transition-colors duration-300 flex items-center space-x-2">
                  <svg v-if="isLoggingOut" class="animate-spin w-4 h-4" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                    </path>
                  </svg>
                  <svg v-else class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                  </svg>
                  <span>{{ isLoggingOut ? 'Signing out...' : 'Sign out' }}</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- Overview Tab -->
      <div v-if="activeTab === 'overview'">
        <!-- Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div
            class="bg-[#FFFFFF] rounded-2xl p-6 shadow-sm border border-[#CCCCCC]/30 hover:shadow-md transition-all duration-300">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-sm font-medium text-[#4B4B4B]">Today's Orders</p>
                <p class="text-3xl font-bold text-[#111111]">{{ stats.todayOrders }}</p>
                <p class="text-sm text-[#2E7D32]">+12% from yesterday</p>
              </div>
              <div class="w-12 h-12 bg-[#2E7D32]/10 rounded-xl flex items-center justify-center">
                <svg class="w-6 h-6 text-[#2E7D32]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                </svg>
              </div>
            </div>
          </div>

          <div
            class="bg-[#FFFFFF] rounded-2xl p-6 shadow-sm border border-[#CCCCCC]/30 hover:shadow-md transition-all duration-300">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-sm font-medium text-[#4B4B4B]">Pending Payments</p>
                <p class="text-3xl font-bold text-[#111111]">{{ stats.pendingPayments }}</p>
                <p class="text-sm text-[#FF8F00]">Requires attention</p>
              </div>
              <div class="w-12 h-12 bg-[#FF8F00]/10 rounded-xl flex items-center justify-center">
                <svg class="w-6 h-6 text-[#FF8F00]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
            </div>
          </div>

          <div
            class="bg-[#FFFFFF] rounded-2xl p-6 shadow-sm border border-[#CCCCCC]/30 hover:shadow-md transition-all duration-300">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-sm font-medium text-[#4B4B4B]">Monthly Revenue</p>
                <p class="text-3xl font-bold text-[#111111]">R{{ stats.monthlyRevenue.toLocaleString() }}</p>
                <p class="text-sm text-[#2E7D32]">+8% from last month</p>
              </div>
              <div class="w-12 h-12 bg-[#C1843E]/10 rounded-xl flex items-center justify-center">
                <svg class="w-6 h-6 text-[#C1843E]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                </svg>
              </div>
            </div>
          </div>

          <div
            class="bg-[#FFFFFF] rounded-2xl p-6 shadow-sm border border-[#CCCCCC]/30 hover:shadow-md transition-all duration-300">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-sm font-medium text-[#4B4B4B]">Low Stock Items</p>
                <p class="text-3xl font-bold text-[#111111]">{{ stats.lowStockItems }}</p>
                <p class="text-sm text-[#C62828]">Need restocking</p>
              </div>
              <div class="w-12 h-12 bg-[#C62828]/10 rounded-xl flex items-center justify-center">
                <svg class="w-6 h-6 text-[#C62828]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
              </div>
            </div>
          </div>
        </div>

        <!-- Recent Orders -->
        <div class="bg-[#FFFFFF] rounded-2xl shadow-sm border border-[#CCCCCC]/30 mb-8">
          <div class="p-6 border-b border-[#CCCCCC]/30">
            <h2 class="text-lg font-semibold text-[#111111]">Recent Orders</h2>
          </div>
          <div class="p-6">
            <div class="space-y-4">
              <div v-for="order in recentOrders" :key="order.id"
                class="flex items-center justify-between p-4 bg-[#F5F5F5] rounded-xl hover:bg-[#EDF6FF] transition-colors duration-200">
                <div class="flex items-center space-x-4">
                  <div
                    class="w-10 h-10 bg-gradient-to-br from-[#1E4E79] to-[#132F4C] rounded-xl flex items-center justify-center">
                    <span class="text-white font-semibold text-sm">#{{ order.id }}</span>
                  </div>
                  <div>
                    <p class="font-medium text-[#111111]">{{ order.customer }}</p>
                    <p class="text-sm text-[#4B4B4B]">{{ order.items }} items • {{ order.time }}</p>
                  </div>
                </div>
                <div class="text-right">
                  <p class="font-semibold text-[#111111]">R{{ order.amount }}</p>
                  <span :class="getOrderStatusClass(order.status)" class="px-2 py-1 rounded-full text-xs font-medium">
                    {{ order.status }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Orders Tab -->
      <div v-if="activeTab === 'orders'">
        <div class="bg-[#FFFFFF] rounded-2xl shadow-sm border border-[#CCCCCC]/30">
          <div class="p-6 border-b border-[#CCCCCC]/30">
            <div class="flex justify-between items-center">
              <h2 class="text-lg font-semibold text-[#111111]">All Orders</h2>
              <div class="flex space-x-2">
                <select
                  class="px-3 py-2 bg-[#F5F5F5] border-0 rounded-xl text-[#111111] focus:ring-2 focus:ring-[#1E4E79]/20">
                  <option>All Status</option>
                  <option>Pending</option>
                  <option>Confirmed</option>
                  <option>Delivered</option>
                </select>
              </div>
            </div>
          </div>
          <div class="p-6">
            <div class="overflow-x-auto">
              <table class="w-full">
                <thead>
                  <tr class="border-b border-[#CCCCCC]/30">
                    <th class="text-left py-3 px-4 font-semibold text-[#111111]">Order ID</th>
                    <th class="text-left py-3 px-4 font-semibold text-[#111111]">Customer</th>
                    <th class="text-left py-3 px-4 font-semibold text-[#111111]">Items</th>
                    <th class="text-left py-3 px-4 font-semibold text-[#111111]">Amount</th>
                    <th class="text-left py-3 px-4 font-semibold text-[#111111]">Status</th>
                    <th class="text-left py-3 px-4 font-semibold text-[#111111]">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="order in allOrders" :key="order.id"
                    class="border-b border-[#CCCCCC]/20 hover:bg-[#F5F5F5] transition-colors duration-200">
                    <td class="py-4 px-4 font-medium text-[#111111]">#{{ order.id }}</td>
                    <td class="py-4 px-4 text-[#111111]">{{ order.customer }}</td>
                    <td class="py-4 px-4 text-[#4B4B4B]">{{ order.items }} items</td>
                    <td class="py-4 px-4 font-semibold text-[#111111]">R{{ order.amount }}</td>
                    <td class="py-4 px-4">
                      <span :class="getOrderStatusClass(order.status)"
                        class="px-3 py-1 rounded-full text-xs font-medium">
                        {{ order.status }}
                      </span>
                    </td>
                    <td class="py-4 px-4">
                      <div class="flex space-x-2">
                        <!-- View Order Details -->
                        <button @click="viewOrderDetails(order)"
                          class="p-2 text-[#1E4E79] hover:bg-[#1E4E79]/10 rounded-lg transition-colors duration-200"
                          title="View Details">
                          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                          </svg>
                        </button>

                        <!-- Accept Order (only for pending orders) -->
                        <button v-if="order.status === 'Pending'" @click="acceptOrder(order.id)"
                          :disabled="processingOrders.includes(order.id)"
                          class="p-2 text-[#2E7D32] hover:bg-[#2E7D32]/10 rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                          title="Accept Order">
                          <svg v-if="processingOrders.includes(order.id)" class="w-4 h-4 animate-spin" fill="none"
                            viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4">
                            </circle>
                            <path class="opacity-75" fill="currentColor"
                              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                            </path>
                          </svg>
                          <svg v-else class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                          </svg>
                        </button>

                        <!-- Cancel Order (only for pending/confirmed orders) -->
                        <button v-if="order.status === 'Pending' || order.status === 'Confirmed'"
                          @click="cancelOrder(order.id)" :disabled="processingOrders.includes(order.id)"
                          class="p-2 text-[#C62828] hover:bg-[#C62828]/10 rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                          title="Cancel Order">
                          <svg v-if="processingOrders.includes(order.id)" class="w-4 h-4 animate-spin" fill="none"
                            viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4">
                            </circle>
                            <path class="opacity-75" fill="currentColor"
                              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                            </path>
                          </svg>
                          <svg v-else class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M6 18L18 6M6 6l12 12" />
                          </svg>
                        </button>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>

      <!-- Inventory Tab -->
      <div v-if="activeTab === 'inventory'">
        <div class="bg-[#FFFFFF] rounded-2xl shadow-sm border border-[#CCCCCC]/30">
          <div class="p-6 border-b border-[#CCCCCC]/30">
            <div class="flex justify-between items-center">
              <h2 class="text-lg font-semibold text-[#111111]">Inventory Management</h2>
              <button @click="showAddInventoryModal = true"
                class="bg-gradient-to-r from-[#1E4E79] to-[#132F4C] text-white px-4 py-2 rounded-xl hover:shadow-lg transition-all duration-300 transform hover:scale-105">
                Add Product
              </button>
            </div>
          </div>
          <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div v-for="product in inventory" :key="product.id"
                class="bg-[#FFFFFF] rounded-2xl shadow-sm border border-[#CCCCCC]/30 overflow-hidden hover:shadow-md transition-all duration-300">
                <!-- Product Image -->
                <div class="relative h-48 bg-[#F5F5F5] overflow-hidden">
                  <img v-if="product.images && product.images.length > 0" :src="product.images[0]" :alt="product.name"
                    class="w-full h-full object-cover" />
                  <div v-else class="w-full h-full flex items-center justify-center">
                    <svg class="w-16 h-16 text-[#CCCCCC]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                  </div>

                  <!-- Image Count Badge -->
                  <div v-if="product.images && product.images.length > 1"
                    class="absolute top-2 right-2 bg-[#111111]/70 text-white text-xs px-2 py-1 rounded-full">
                    +{{ product.images.length - 1 }}
                  </div>

                  <!-- Stock Status Badge -->
                  <div class="absolute top-2 left-2">
                    <span :class="getStockStatusClass(product.stock)"
                      class="px-2 py-1 rounded-full text-xs font-medium">
                      {{ getStockStatus(product.stock) }}
                    </span>
                  </div>
                </div>

                <!-- Product Details -->
                <div class="p-4">
                  <div class="flex items-center justify-between mb-3">
                    <h3 class="font-semibold text-[#111111] truncate">{{ product.name }}</h3>
                    <button @click="uploadProductImages(product)"
                      class="p-1 text-[#4B4B4B] hover:text-[#1E4E79] transition-colors duration-300"
                      title="Upload Images">
                      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12" />
                      </svg>
                    </button>
                  </div>

                  <div class="space-y-2 mb-4">
                    <div class="flex justify-between">
                      <span class="text-[#4B4B4B] text-sm">Price:</span>
                      <span class="font-medium text-[#111111]">R{{ product.price }}</span>
                    </div>
                    <div class="flex justify-between">
                      <span class="text-[#4B4B4B] text-sm">Stock:</span>
                      <span class="font-medium text-[#111111]">{{ product.stock }} units</span>
                    </div>
                    <div class="flex justify-between">
                      <span class="text-[#4B4B4B] text-sm">Sold:</span>
                      <span class="font-medium text-[#111111]">{{ product.sold }} units</span>
                    </div>
                  </div>

                  <div class="flex space-x-2">
                    <button @click="editProduct(product)"
                      class="flex-1 bg-[#1E4E79] text-white py-2 px-3 rounded-xl hover:bg-[#132F4C] transition-colors duration-300 text-sm">
                      Edit
                    </button>
                    <button @click="restockProduct(product)"
                      class="flex-1 bg-[#C1843E] text-white py-2 px-3 rounded-xl hover:bg-[#704A1F] transition-colors duration-300 text-sm">
                      Restock
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Reports Tab -->
      <div v-if="activeTab === 'reports'">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div class="bg-[#FFFFFF] rounded-2xl shadow-sm border border-[#CCCCCC]/30 p-6">
            <h3 class="text-lg font-semibold text-[#111111] mb-4">Sales Report</h3>
            <div class="h-64 bg-[#F5F5F5] rounded-xl flex items-center justify-center">
              <p class="text-[#4B4B4B]">Sales Chart Placeholder</p>
            </div>
          </div>

          <div class="bg-[#FFFFFF] rounded-2xl shadow-sm border border-[#CCCCCC]/30 p-6">
            <h3 class="text-lg font-semibold text-[#111111] mb-4">Top Products</h3>
            <div class="space-y-4">
              <div v-for="product in topProducts" :key="product.id"
                class="flex items-center justify-between p-3 bg-[#F5F5F5] rounded-xl">
                <div>
                  <p class="font-medium text-[#111111]">{{ product.name }}</p>
                  <p class="text-sm text-[#4B4B4B]">{{ product.sold }} sold</p>
                </div>
                <div class="text-right">
                  <p class="font-semibold text-[#111111]">R{{ product.revenue.toLocaleString() }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Income Statement Tab -->
      <div v-if="activeTab === 'income'">
        <div class="space-y-6">
          <!-- Income Statement Header -->
          <div class="bg-[#FFFFFF] rounded-2xl shadow-sm border border-[#CCCCCC]/30 p-6">
            <div class="flex justify-between items-center mb-6">
              <h2 class="text-xl font-bold text-[#111111]">Income Statement</h2>
              <div class="flex space-x-3">
                <select v-model="incomeStatementPeriod" @change="loadIncomeStatement"
                  class="px-3 py-2 bg-[#F5F5F5] border-0 rounded-xl text-[#111111] focus:ring-2 focus:ring-[#1E4E79]/20">
                  <option value="30">Last 30 Days</option>
                  <option value="90">Last 3 Months</option>
                  <option value="365">Last Year</option>
                  <option value="custom">Custom Range</option>
                </select>
                <button @click="exportIncomeStatement"
                  class="px-4 py-2 bg-[#C1843E] text-white rounded-xl hover:bg-[#704A1F] transition-colors duration-300">
                  Export PDF
                </button>
              </div>
            </div>

            <!-- Financial Summary Cards -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
              <div
                class="bg-gradient-to-br from-[#2E7D32]/10 to-[#2E7D32]/5 rounded-2xl p-6 border border-[#2E7D32]/20">
                <div class="flex items-center justify-between">
                  <div>
                    <p class="text-sm font-medium text-[#2E7D32]">Total Revenue</p>
                    <p class="text-2xl font-bold text-[#2E7D32]">R{{ incomeData.totalRevenue.toLocaleString() }}</p>
                    <p class="text-xs text-[#2E7D32]/70">{{ incomeStatementPeriod }} days</p>
                  </div>
                  <div class="w-12 h-12 bg-[#2E7D32]/20 rounded-xl flex items-center justify-center">
                    <svg class="w-6 h-6 text-[#2E7D32]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                    </svg>
                  </div>
                </div>
              </div>

              <div
                class="bg-gradient-to-br from-[#FF8F00]/10 to-[#FF8F00]/5 rounded-2xl p-6 border border-[#FF8F00]/20">
                <div class="flex items-center justify-between">
                  <div>
                    <p class="text-sm font-medium text-[#FF8F00]">Total Expenses</p>
                    <p class="text-2xl font-bold text-[#FF8F00]">R{{ incomeData.totalExpenses.toLocaleString() }}</p>
                    <p class="text-xs text-[#FF8F00]/70">{{ incomeStatementPeriod }} days</p>
                  </div>
                  <div class="w-12 h-12 bg-[#FF8F00]/20 rounded-xl flex items-center justify-center">
                    <svg class="w-6 h-6 text-[#FF8F00]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M13 17h8m0 0V9m0 8l-8-8-4 4-6-6" />
                    </svg>
                  </div>
                </div>
              </div>

              <div
                class="bg-gradient-to-br from-[#1E4E79]/10 to-[#1E4E79]/5 rounded-2xl p-6 border border-[#1E4E79]/20">
                <div class="flex items-center justify-between">
                  <div>
                    <p class="text-sm font-medium text-[#1E4E79]">Net Profit</p>
                    <p class="text-2xl font-bold"
                      :class="incomeData.netProfit >= 0 ? 'text-[#2E7D32]' : 'text-[#C62828]'">
                      R{{ incomeData.netProfit.toLocaleString() }}
                    </p>
                    <p class="text-xs text-[#1E4E79]/70">{{ ((incomeData.netProfit / incomeData.totalRevenue) *
                      100).toFixed(1) }}% margin</p>
                  </div>
                  <div class="w-12 h-12 bg-[#1E4E79]/20 rounded-xl flex items-center justify-center">
                    <svg class="w-6 h-6 text-[#1E4E79]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                    </svg>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Detailed Income Statement -->
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Revenue Breakdown -->
            <div class="bg-[#FFFFFF] rounded-2xl shadow-sm border border-[#CCCCCC]/30 p-6">
              <h3 class="text-lg font-semibold text-[#111111] mb-4">Revenue Breakdown</h3>
              <div class="space-y-4">
                <div v-for="item in incomeData.revenueBreakdown" :key="item.category"
                  class="flex items-center justify-between p-3 bg-[#F5F5F5] rounded-xl">
                  <div class="flex items-center space-x-3">
                    <div class="w-3 h-3 rounded-full" :style="{ backgroundColor: item.color }"></div>
                    <span class="font-medium text-[#111111]">{{ item.category }}</span>
                  </div>
                  <div class="text-right">
                    <p class="font-semibold text-[#111111]">R{{ item.amount.toLocaleString() }}</p>
                    <p class="text-xs text-[#4B4B4B]">{{ ((item.amount / incomeData.totalRevenue) * 100).toFixed(1) }}%
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <!-- Expense Breakdown -->
            <div class="bg-[#FFFFFF] rounded-2xl shadow-sm border border-[#CCCCCC]/30 p-6">
              <h3 class="text-lg font-semibold text-[#111111] mb-4">Expense Breakdown</h3>
              <div class="space-y-4">
                <div v-for="item in incomeData.expenseBreakdown" :key="item.category"
                  class="flex items-center justify-between p-3 bg-[#F5F5F5] rounded-xl">
                  <div class="flex items-center space-x-3">
                    <div class="w-3 h-3 rounded-full" :style="{ backgroundColor: item.color }"></div>
                    <span class="font-medium text-[#111111]">{{ item.category }}</span>
                  </div>
                  <div class="text-right">
                    <p class="font-semibold text-[#111111]">R{{ item.amount.toLocaleString() }}</p>
                    <p class="text-xs text-[#4B4B4B]">{{ ((item.amount / incomeData.totalExpenses) * 100).toFixed(1) }}%
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Monthly Trend Chart -->
          <div class="bg-[#FFFFFF] rounded-2xl shadow-sm border border-[#CCCCCC]/30 p-6">
            <h3 class="text-lg font-semibold text-[#111111] mb-4">Monthly Profit Trend</h3>
            <div class="h-64 bg-[#F5F5F5] rounded-xl flex items-center justify-center">
              <p class="text-[#4B4B4B]">Profit Trend Chart Placeholder</p>
            </div>
          </div>
        </div>
      </div>
    </main>

    <!-- Add Inventory Modal -->
    <AddInventoryModal :is-open="showAddInventoryModal" @close="showAddInventoryModal = false"
      @product-added="handleProductAdded" />
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { authService, orderService, dashboardService, inventoryService } from '../services/podApi.js'
import AddInventoryModal from '../components/AddInventoryModal.vue'

const router = useRouter()

// Reactive data
const activeTab = ref('overview')
const businessName = ref('Fresh Groceries')
const showUserMenu = ref(false)
const isLoggingOut = ref(false)
const userMenuRef = ref(null)
const showAddInventoryModal = ref(false)
const processingOrders = ref([])
const incomeStatementPeriod = ref('30')

// Income statement data
const incomeData = ref({
  totalRevenue: 45600,
  totalExpenses: 32400,
  netProfit: 13200,
  revenueBreakdown: [
    { category: 'Product Sales', amount: 38500, color: '#2E7D32' },
    { category: 'Delivery Fees', amount: 4200, color: '#1E4E79' },
    { category: 'Service Charges', amount: 2900, color: '#C1843E' }
  ],
  expenseBreakdown: [
    { category: 'Cost of Goods', amount: 18500, color: '#FF8F00' },
    { category: 'Delivery Costs', amount: 6200, color: '#C62828' },
    { category: 'Platform Fees', amount: 4800, color: '#704A1F' },
    { category: 'Marketing', amount: 2900, color: '#132F4C' }
  ]
})

const stats = ref({
  todayOrders: 12,
  pendingPayments: 3,
  monthlyRevenue: 25400,
  lowStockItems: 5
})

const recentOrders = ref([
  { id: 1001, customer: 'John Smith', items: 3, amount: 450, status: 'Pending', time: '2 hours ago' },
  { id: 1002, customer: 'Sarah Johnson', items: 5, amount: 720, status: 'Confirmed', time: '4 hours ago' },
  { id: 1003, customer: 'Mike Brown', items: 2, amount: 280, status: 'Delivered', time: '6 hours ago' }
])

const allOrders = ref([
  { id: 1001, customer: 'John Smith', items: 3, amount: 450, status: 'Pending' },
  { id: 1002, customer: 'Sarah Johnson', items: 5, amount: 720, status: 'Confirmed' },
  { id: 1003, customer: 'Mike Brown', items: 2, amount: 280, status: 'Delivered' },
  { id: 1004, customer: 'Lisa Davis', items: 4, amount: 620, status: 'Pending' }
])

const inventory = ref([
  {
    id: 1,
    name: 'Fresh Apples',
    price: 25,
    stock: 50,
    sold: 120,
    images: ['https://images.unsplash.com/photo-1560806887-1e4cd0b6cbd6?w=400&h=300&fit=crop']
  },
  {
    id: 2,
    name: 'Organic Bananas',
    price: 18,
    stock: 5,
    sold: 85,
    images: ['https://images.unsplash.com/photo-1571771894821-ce9b6c11b08e?w=400&h=300&fit=crop']
  },
  {
    id: 3,
    name: 'Premium Bread',
    price: 35,
    stock: 25,
    sold: 95,
    images: []
  }
])

const topProducts = ref([
  { id: 1, name: 'Fresh Apples', sold: 120, revenue: 3000 },
  { id: 2, name: 'Premium Bread', sold: 95, revenue: 3325 },
  { id: 3, name: 'Organic Bananas', sold: 85, revenue: 1530 }
])

// Methods
const getOrderStatusClass = (status) => {
  switch (status) {
    case 'Pending':
      return 'bg-[#FF8F00]/10 text-[#FF8F00]'
    case 'Confirmed':
      return 'bg-[#1E4E79]/10 text-[#1E4E79]'
    case 'Delivered':
      return 'bg-[#2E7D32]/10 text-[#2E7D32]'
    case 'Cancelled':
      return 'bg-[#C62828]/10 text-[#C62828]'
    default:
      return 'bg-[#4B4B4B]/10 text-[#4B4B4B]'
  }
}

const getStockStatus = (stock) => {
  if (stock <= 10) return 'Low Stock'
  if (stock <= 25) return 'Medium'
  return 'In Stock'
}

const getStockStatusClass = (stock) => {
  if (stock <= 10) return 'bg-[#C62828]/10 text-[#C62828]'
  if (stock <= 25) return 'bg-[#FF8F00]/10 text-[#FF8F00]'
  return 'bg-[#2E7D32]/10 text-[#2E7D32]'
}

// Logout functionality
const handleLogout = async () => {
  if (isLoggingOut.value) return

  isLoggingOut.value = true
  showUserMenu.value = false

  try {
    await authService.logout()
    router.push('/login')
  } catch (error) {
    console.error('Logout error:', error)
    // Still redirect to login even if API call fails
    router.push('/login')
  } finally {
    isLoggingOut.value = false
  }
}

// Handle product added
const handleProductAdded = (newProduct) => {
  // Add the new product to the inventory list
  inventory.value.push({
    id: newProduct.id,
    name: newProduct.name,
    price: newProduct.price,
    stock: newProduct.stock_quantity,
    sold: 0 // New products start with 0 sold
  })

  // Close the modal
  showAddInventoryModal.value = false

  // Show success message (you could add a toast notification here)
  console.log('Product added successfully:', newProduct)
}

// Order Management Methods
const viewOrderDetails = (order) => {
  // For now, just log the order details
  // In a real app, you might open a modal with detailed order information
  console.log('Viewing order details:', order)
  alert(`Order #${order.id}\nCustomer: ${order.customer}\nItems: ${order.items}\nAmount: R${order.amount}\nStatus: ${order.status}`)
}

const acceptOrder = async (orderId) => {
  if (processingOrders.value.includes(orderId)) return

  processingOrders.value.push(orderId)

  try {
    await orderService.acceptOrder(orderId)

    // Update the order status in the local data
    const orderIndex = allOrders.value.findIndex(order => order.id === orderId)
    if (orderIndex !== -1) {
      allOrders.value[orderIndex].status = 'Confirmed'
    }

    // Also update recent orders if it exists there
    const recentOrderIndex = recentOrders.value.findIndex(order => order.id === orderId)
    if (recentOrderIndex !== -1) {
      recentOrders.value[recentOrderIndex].status = 'Confirmed'
    }

    console.log('Order accepted successfully')
  } catch (error) {
    console.error('Error accepting order:', error)
    alert('Failed to accept order: ' + (error.message || 'Unknown error'))
  } finally {
    processingOrders.value = processingOrders.value.filter(id => id !== orderId)
  }
}

const cancelOrder = async (orderId) => {
  if (processingOrders.value.includes(orderId)) return

  const reason = prompt('Please provide a reason for cancellation (optional):')
  if (reason === null) return // User cancelled the prompt

  processingOrders.value.push(orderId)

  try {
    await orderService.cancelOrder(orderId, reason)

    // Update the order status in the local data
    const orderIndex = allOrders.value.findIndex(order => order.id === orderId)
    if (orderIndex !== -1) {
      allOrders.value[orderIndex].status = 'Cancelled'
    }

    // Also update recent orders if it exists there
    const recentOrderIndex = recentOrders.value.findIndex(order => order.id === orderId)
    if (recentOrderIndex !== -1) {
      recentOrders.value[recentOrderIndex].status = 'Cancelled'
    }

    console.log('Order cancelled successfully')
  } catch (error) {
    console.error('Error cancelling order:', error)
    alert('Failed to cancel order: ' + (error.message || 'Unknown error'))
  } finally {
    processingOrders.value = processingOrders.value.filter(id => id !== orderId)
  }
}

// Income Statement Methods
const loadIncomeStatement = async () => {
  try {
    // Calculate date range based on selected period
    const endDate = new Date()
    const startDate = new Date()

    if (incomeStatementPeriod.value === 'custom') {
      // For now, just use 30 days for custom
      startDate.setDate(endDate.getDate() - 30)
    } else {
      startDate.setDate(endDate.getDate() - parseInt(incomeStatementPeriod.value))
    }

    const data = await dashboardService.getIncomeStatement(
      startDate.toISOString().split('T')[0],
      endDate.toISOString().split('T')[0]
    )

    // Update income data with API response
    if (data) {
      incomeData.value = {
        ...incomeData.value,
        ...data
      }
    }
  } catch (error) {
    console.error('Error loading income statement:', error)
    // Keep using mock data if API fails
  }
}

const exportIncomeStatement = () => {
  // For now, just show an alert
  // In a real app, this would generate and download a PDF
  alert('Income statement export functionality would be implemented here. This would generate a PDF with the current financial data.')

  // Example of what the export might look like:
  console.log('Exporting income statement:', {
    period: incomeStatementPeriod.value,
    data: incomeData.value,
    generatedAt: new Date().toISOString()
  })
}

// Product Management Methods
const editProduct = (product) => {
  // For now, just show an alert with product details
  // In a real app, this would open an edit modal
  alert(`Edit Product: ${product.name}\nPrice: R${product.price}\nStock: ${product.stock}\n\nEdit functionality would be implemented here.`)
}

const restockProduct = (product) => {
  // Simple restock functionality
  const additionalStock = prompt(`How many units would you like to add to ${product.name}?\nCurrent stock: ${product.stock}`)

  if (additionalStock && !isNaN(additionalStock) && parseInt(additionalStock) > 0) {
    const productIndex = inventory.value.findIndex(p => p.id === product.id)
    if (productIndex !== -1) {
      inventory.value[productIndex].stock += parseInt(additionalStock)
      alert(`Successfully added ${additionalStock} units to ${product.name}.\nNew stock: ${inventory.value[productIndex].stock}`)
    }
  }
}

const uploadProductImages = (product) => {
  // Create a file input element
  const fileInput = document.createElement('input')
  fileInput.type = 'file'
  fileInput.multiple = true
  fileInput.accept = 'image/*'

  fileInput.onchange = async (event) => {
    const files = Array.from(event.target.files)

    if (files.length === 0) return

    try {
      // Show loading state (in a real app, you'd have a proper loading indicator)
      console.log(`Uploading ${files.length} images for ${product.name}...`)

      // Upload images one by one
      const uploadedImages = []
      for (const file of files) {
        try {
          // In a real app, you'd call the API
          // const result = await inventoryService.uploadProductImage(product.id, file)

          // For demo purposes, create a local URL
          const imageUrl = URL.createObjectURL(file)
          uploadedImages.push(imageUrl)
        } catch (error) {
          console.error('Error uploading image:', error)
        }
      }

      // Update the product with new images
      const productIndex = inventory.value.findIndex(p => p.id === product.id)
      if (productIndex !== -1) {
        if (!inventory.value[productIndex].images) {
          inventory.value[productIndex].images = []
        }
        inventory.value[productIndex].images.push(...uploadedImages)

        alert(`Successfully uploaded ${uploadedImages.length} images for ${product.name}`)
      }

    } catch (error) {
      console.error('Error uploading images:', error)
      alert('Failed to upload images: ' + (error.message || 'Unknown error'))
    }
  }

  // Trigger file selection
  fileInput.click()
}

// Close user menu when clicking outside
const handleClickOutside = (event) => {
  if (userMenuRef.value && !userMenuRef.value.contains(event.target)) {
    showUserMenu.value = false
  }
}

onMounted(() => {
  console.log('Pod Dashboard loaded')
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>
